<cfsavecontent variable="local.additionalDataTabJS">
	<cfoutput>
	<script type="text/javascript">
		function toggleContactInfoUnderSameFirm(x) {
			if (x == 1) { 
				$('div.contactInfoRow').show();
			} else {
				$('div.contactInfoRow').hide();
				clearFieldSetSelection_#local.strProspectContactInfoFSSelector.selectorID#();
				saveProspectContactInfoSettings();
			}
		}
		function saveProspectContactInfoSettings() {
			mca_hideAlert('err_prospectcontact');
			var saveResult = function(r) {
				$('##btnSaveProspectContactSettings').html('Save').prop('disabled',false);
				if (!(r.success && r.success.toLowerCase() == 'true')){
					mca_showAlert('err_prospectcontact', 'We were unable to save #local.qryProject.prospectFieldLabel# contacts settings.');
				}
			};
			
			if ($('input[name="showContactInfoUnderSameFirm"]:checked').val() == 1 && $('###local.strProspectContactInfoFSSelector.selectorID#').val() == 0) {
				mca_showAlert('err_prospectcontact', 'Select a field set.');
				return false;
			}

			$('##btnSaveProspectContactSettings').html('Saving...').prop('disabled',true);

			var objParams = { projectID:#arguments.event.getValue('projectID')#, prospectContactInfoFieldSetID:$('###local.strProspectContactInfoFSSelector.selectorID#').val() };
			TS_AJX('ADMINPROJECT','saveProspectContactInfoSettings',objParams,saveResult,saveResult,10000,saveResult);
		}
		function saveProjectExtraDetails() {
			var saveResult = function(r) {
				if (!(r.success && r.success.toLowerCase() == 'true')){
					alert('Some error occurred while saving #local.qryProject.prospectFieldLabel# Data.');
				}
			};

			var objParams = { projectID:#arguments.event.getValue('projectID')#, rptid:#arguments.event.getValue('reportID')#, csrid:#arguments.event.getValue('siteResourceID')#, 
								frmgivinghistoryfrom:$('##frmgivinghistoryfrom').val(), frmgivinghistoryto:$('##frmgivinghistoryto').val(), frmpaymentsummaryfrom:$('##frmpaymentsummaryfrom').val(), 
								frmpaymentsummaryto:$('##frmpaymentsummaryto').val(), frmlinkalloctype:$('##frmlinkalloctype').val() };
			TS_AJX('ADMINPROJECT','saveProjectExtraDetails',objParams,saveResult,saveResult,10000,saveResult);
		}

		$(function(){
			mca_setupDatePickerRangeFields('frmgivinghistoryfrom','frmgivinghistoryto');
			mca_setupDatePickerRangeFields('frmpaymentsummaryfrom','frmpaymentsummaryto');
			mca_setupCalendarIcons('additionalProspectDataContainer');

			$('div.projectdate').after('<div class="cursor-pointer ml-1"><i class="fa-regular fa-calendar-xmark text-danger clearProjectDate" title="Clear Date"></i></div>');
			$('.clearProjectDate').click(function() {
				$(this).closest('.dateFieldCol').find('.dateControl').val('');
				saveProjectExtraDetails();
			});
			$('###local.strProspectContactInfoFSSelector.selectorID#').change(function(){
				saveProspectContactInfoSettings();
			});
		});
	</script>
	#local.strGivingHistGLAcctWidget.js#
	#local.strPaymentSummaryGLAcctWidget.js#
	#local.strSubscriptionWidget.js#
	#local.strEventWidget.js#
	<cfloop list="#local.mhTypeIDList#" index="local.typeID">#local.strMHWidget[local.typeID].js#</cfloop>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.additionalDataTabJS#">

<cfoutput>
	<div class="card card-box mb-1">
		<div class="card-header py-1 bg-light">
			<div class="card-header--title font-weight-bold font-size-lg">
				Show Information for contacts from same firm
			</div>
		</div>
		<div class="card-body pb-3">
			<cfform id="frmProspectContactSettings" name="frmProspectContactSettings">
				<div id="err_prospectcontact" class="alert alert-danger mb-2 d-none"></div>
				<div class="form-group row">
					<label class="col-sm-4 col-form-label-sm font-size-md pt-0">
						Show #local.qryProject.solicitorFieldLabelPlural# records from the same firm on #local.qryProject.taskFieldLabel# detail screen:
					</label>
					<div class="col-sm-8">
						<div class="form-check form-check-inline">
							<input type="radio" name="showContactInfoUnderSameFirm" id="showContactInfoUnderSameFirmYes" class="form-check-input" value="1" onclick="toggleContactInfoUnderSameFirm(1);"<cfif local.showContactInfoUnderSameFirm> checked</cfif>>
							<label class="form-check-label" for="showContactInfoUnderSameFirmYes">Yes</label>
						</div>
						<div class="form-check form-check-inline">
							<input type="radio" name="showContactInfoUnderSameFirm" id="showContactInfoUnderSameFirmNo" class="form-check-input" value="0" onclick="toggleContactInfoUnderSameFirm(0);"<cfif not local.showContactInfoUnderSameFirm> checked</cfif>>
							<label class="form-check-label" for="showContactInfoUnderSameFirmNo">No</label>
						</div>
					</div>
				</div>

				<div class="contactInfoRow mb-5"<cfif not local.showContactInfoUnderSameFirm> style="display:none;"</cfif>>
					<div class="form-group row">
						<label for="" class="col-sm-4 col-form-label-sm font-size-md">Field Set: *</label>
						<div class="col-sm-8">
							#local.strProspectContactInfoFSSelector.html#
							<div class="form-text small">(First Name, Last Name, MemberNumber, and Company always appear.)</div>
						</div>
					</div>

					<div class="mt-3">
						<label class="font-size-md">Group Sets:</label>
						<div class="mt-2">
							#local.prospectContactGroupSetWidget.html#
						</div>
					</div>
				</div>
			</cfform>
		</div>
	</div>

	<div class="card card-box mt-3 mb-1" id="additionalProspectDataContainer">
		<div class="card-header py-1 bg-light">
			<div class="card-header--title font-weight-bold font-size-lg">
				Addtional #local.qryProject.prospectFieldLabel# Data to Show #local.qryProject.solicitorFieldLabelPlural#
			</div>
		</div>
		<div class="card-body pb-3">
			<div id="divGivingHistGLAcctWidget" class="p-2 mb-3">
				#local.strGivingHistGLAcctWidget.html#
				<div class="stepInfo my-3">
					<div class="form-group row">
						<label for="frmgivinghistoryfrom" class="col-sm-3 col-form-label-sm font-size-md">Date range</label>
						<div class="col-sm-9">
							<div class="row align-items-center">
								<div class="col-md pr-md-0 d-flex align-items-center dateFieldCol">
									<div class="input-group input-group-sm projectdate">
										<input type="text" name="frmgivinghistoryfrom" id="frmgivinghistoryfrom" value="#local.frmgivinghistoryfrom#" class="form-control form-control-sm dateControl" placeholder="Start Date" onchange="saveProjectExtraDetails();">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="frmgivinghistoryfrom"><i class="fa-solid fa-calendar"></i></span>
										</div>
									</div>
								</div>
								<div class="col-md-auto px-md-2">to</div>
								<div class="col-md pl-md-0 d-flex align-items-center dateFieldCol">
									<div class="input-group input-group-sm projectdate">
										<input type="text" name="frmgivinghistoryto" id="frmgivinghistoryto" value="#local.frmgivinghistoryto#" class="form-control form-control-sm dateControl" placeholder="End Date" onchange="saveProjectExtraDetails();">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="frmgivinghistoryto"><i class="fa-solid fa-calendar"></i></span>
										</div>
									</div>
								</div>
								<div class="col-12 mt-1"><i>(We'll compare to the same range in previous years.)</i></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div id="divPmtSummGLAcctWidget" class="p-2 mb-3">
				#local.strPaymentSummaryGLAcctWidget.html#
				<div class="stepInfo my-3">
					<div class="form-group row">
						<label for="frmpaymentsummaryfrom" class="col-sm-3 col-form-label-sm font-size-md">Batch Deposit Date</label>
						<div class="col-sm-9">
							<div class="row align-items-center">
								<div class="col-md pr-md-0 d-flex align-items-center dateFieldCol">
									<div class="input-group input-group-sm projectdate">
										<input type="text" name="frmpaymentsummaryfrom" id="frmpaymentsummaryfrom" value="#local.frmpaymentsummaryfrom#" class="form-control form-control-sm dateControl" placeholder="Start Date" onchange="saveProjectExtraDetails();">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="frmpaymentsummaryfrom"><i class="fa-solid fa-calendar"></i></span>
										</div>
									</div>
								</div>
								<div class="col-md-auto px-md-2">to</div>
								<div class="col-md pl-md-0 d-flex align-items-center dateFieldCol">
									<div class="input-group input-group-sm projectdate">
										<input type="text" name="frmpaymentsummaryto" id="frmpaymentsummaryto" value="#local.frmpaymentsummaryto#" class="form-control form-control-sm dateControl" placeholder="End Date" onchange="saveProjectExtraDetails();">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="frmpaymentsummaryto"><i class="fa-solid fa-calendar"></i></span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="form-group row">
						<label for="frmlinkalloctype" class="col-sm-3 col-form-label-sm font-size-md">Link Allocation Type</label>
						<div class="col-sm-9">
							<select name="frmlinkalloctype" id="frmlinkalloctype" class="form-control form-control-sm" onchange="saveProjectExtraDetails();">
								<option value="LinkAllocCash" <cfif local.frmLinkAllocType eq "LinkAllocCash">selected</cfif>>Members Linked to Allocation's Cash</option>
								<option value="LinkAllocRevenue" <cfif local.frmLinkAllocType eq "LinkAllocRevenue">selected</cfif>>Members Linked to Allocation's Revenue</option>	
							</select>
						</div>
					</div>
				</div>
			</div>

			<div id="divSubsWidget" class="p-2 mb-3">
				#local.strSubscriptionWidget.html#
			</div>

			<div id="divEvWidget" class="p-2 mb-3">
				#local.strEventWidget.html#
			</div>

			<cfloop list="#local.mhTypeIDList#" index="local.typeID">
				<div id="divMHWidget#local.typeID#" class="p-2 mb-3">
					#local.strMHWidget[local.typeID].html#
				</div>
			</cfloop>
		</div>
	</div>
</cfoutput>